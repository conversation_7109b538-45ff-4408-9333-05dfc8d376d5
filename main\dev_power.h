/*
 * Device power Control Header
 * power GPIO control and blink functionality
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C" {
#endif

/* power状态定义 */
typedef enum {
    POWER_OFF = 0,    /* power关闭 */
    POWER_ON = 1      /* power开启 */
} power_state_t;

/**
 * @brief 初始化power GPIO
 *
 */
esp_err_t dev_power_init(void);


#ifdef __cplusplus
}
#endif