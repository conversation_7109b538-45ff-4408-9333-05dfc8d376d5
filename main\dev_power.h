/*
 * Device power Control Header
 * power GPIO control and blink functionality
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C" {
#endif


/* power状态定义 */
typedef enum {
    POWER_OFF = 0,    /* power关闭 */
    POWER_ON = 1      /* power开启 */
} power_state_t;

/**
 * @brief 初始化power GPIO
 *
 * 配置power_GPIO为输出模式，并设置初始状态为关闭
 *
 * @return
 *     - ESP_OK: 初始化成功
 *     - 其他: 初始化失败的错误码
 */
esp_err_t dev_power_init(void);

/**
 * @brief 创建power闪烁任务
 *
 * 创建一个FreeRTOS任务，让power以指定间隔闪烁
 * @param task_priority 任务优先级
 * @return
 *     - ESP_OK: 任务创建成功
 *     - ESP_FAIL: 任务创建失败
 */
esp_err_t dev_power_start_task( uint8_t task_priority);



#ifdef __cplusplus
}
#endif