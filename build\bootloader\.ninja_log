# ninja log v6
56	331	7758435835979802	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	e15820dea774621b
67	372	7758435836079730	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	af432d4b589ee1b5
133	518	7758435836743642	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	84131d17d4ce8051
449	1308	7758435839908259	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	6117e351300cbe3d
119	536	7758435836608513	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	f8c0c43f38767e10
107	412	7758435836486283	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	af1371b6488c4d52
3414	3724	7758435869555199	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	bc9a654625a7d45f
31	448	7758435835725345	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	1c5e934baaf54c2a
3668	3839	7758435872090962	esp-idf/efuse/libefuse.a	34285ec1d70ea8cb
15	470	7758435835575329	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	ba5714f7c4cdf445
78	489	7758435836196152	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a86d33291ee8e0f6
2378	2777	7758435859198181	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	4671c0eca56bdd24
45	507	7758435835866950	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	ece775a80cae7ca8
90	561	7758435836326242	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	59e7541cb455c098
3140	3414	7758435866821700	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	a874c8d017429cdb
2097	2791	7758435856394427	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	25d3c6fa37f92267
2359	2840	7758435859004144	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	4d46a0b3d70d48c2
181	577	7758435837229495	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	58f1bd03d1f0a630
206	591	7758435837475817	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	88ccfd9567c65b09
151	625	7758435836923409	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	654c0bde84e538c2
1324	2137	7758435848660733	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	fe342b02e74d6afb
165	767	7758435837069253	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	6c3539b9a6b2c090
2049	2558	7758435855904319	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	49e9e9fbac3d2cc6
2901	3344	7758435864430967	esp-idf/esp_hw_support/libesp_hw_support.a	3679b8a92846a693
333	841	7758435838747548	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	47e59a816f7625a3
372	1263	7758435839138089	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	e3713d4c02030940
518	1277	7758435840595983	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	88f2d8e65714c39e
1244	2064	7758435847850550	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	26e5983b80ca800c
470	1291	7758435840116046	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	5cf2d9b9c07da15e
561	1324	7758435841025641	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	fe1a0931a37d6b57
2121	2857	7758435856624482	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	48a88e9ebb86f8e4
490	1344	7758435840315987	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	953c498b1adadd49
592	1360	7758435841335577	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	ce90180a239d05b
2740	3173	7758435862807415	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	371043d7e43101a3
507	1558	7758435840485957	esp-idf/log/liblog.a	fe2b739ec423e137
626	2049	7758435841675645	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	cede0d3902475d80
536	1824	7758435840776083	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	44b23d4f400bd499
3381	3609	7758435869225118	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	ac2e655e0fae1903
1970	2621	7758435855111663	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	4e40c4e2e2df620a
412	1970	7758435839538178	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	ba32363c553f7015
577	1985	7758435841185536	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8fc06b614024f89
1277	2097	7758435848190624	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	23146cbd7e327ee3
1291	2121	7758435848320658	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	f033473e85a84d38
1263	2265	7758435848040591	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ff36edc5475c4bf2
2840	3250	7758435863812329	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	80ffd329b68e65e8
1345	2151	7758435848856909	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	4394466c369f198b
2064	2926	7758435856054350	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	54ac8e2b2f285178
1360	2166	7758435849013511	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	cbd651149b2cb169
2400	2901	7758435859418211	esp-idf/esp_common/libesp_common.a	69bea9b40d86bb8a
2874	3321	7758435864156359	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	5f1792f5df4b7e2e
768	2220	7758435843095246	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	eddb1aa078ae624d
1985	2378	7758435855271702	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	241630bfcf729afc
1308	2285	7758435848500700	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	f4f52ffd00d2f4ef
2777	3279	7758435863177494	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	4bc85f735ee751ab
1824	2359	7758435853655590	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	698d737a68f0a1d8
2814	3213	7758435863556811	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9c73e250e045db3
1559	2400	7758435851001300	esp-idf/esp_rom/libesp_rom.a	32b412aa87dd51b4
2266	2739	7758435858067002	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	b1bf21cce72e6ff9
13	89	7758445521003392	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
3230	3527	7758435867717764	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	4f37d4a02e7d0249
2220	2874	7758435857643815	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	4fe25f4054a9fe21
2151	2754	7758435856924545	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	c4aed1deaef6fbb5
2285	2814	7758435858268935	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	a470e45fce1e9393
2166	3189	7758435857074583	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	1ad156bfad3007fc
2558	2945	7758435860992671	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	3bac9c70bd41b28e
2755	3124	7758435862957453	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	2436b3fba4fc9b02
2138	3140	7758435856794532	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8a6bbef15d0628b4
2791	3157	7758435863327533	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	7a70e5919f8f8c4a
2622	3230	7758435861633496	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	428e14fa34f35797
2857	3364	7758435863987529	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	6ca5876b8f71aae4
2926	3381	7758435864671015	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	8c4abbc62e23a5fc
3124	3399	7758435866659023	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	89e75e9cd4af9572
3157	3434	7758435866981089	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	31f70329aeb04070
3189	3462	7758435867306246	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	f89c116343a1b29f
3173	3486	7758435867146219	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	a061659f6398e4f6
3213	3508	7758435867547796	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	165e6058b97dc64e
3250	3555	7758435867917845	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	bc7fd951bb0cb5ad
3279	3574	7758435868208112	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	530ce939082b6841
3321	3594	7758435868618214	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	5d478cf33beeb1ec
3365	3618	7758435869065079	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	bf276d663340ad7b
3344	3668	7758435868855038	esp-idf/esp_system/libesp_system.a	1dd941b058717bae
3434	3703	7758435869762110	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	5e20081e045c61cb
3399	3712	7758435869405157	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	778b326b9de37a92
4518	4658	7758435880597400	esp-idf/soc/libsoc.a	2b6f598c40608a33
3509	3731	7758435870497469	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	c9717b403e693f23
3462	3734	7758435870042179	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	2905f6afb6d66aca
3486	3736	7758435870282231	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	b88b2cc06ab600e6
3555	3736	7758435872711568	project_elf_src_esp32c3.c	65d6441927cf4379
3555	3736	7758435872711568	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	65d6441927cf4379
3528	3750	7758435870697521	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	d77882303108f5f9
3575	3806	7758435871159079	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	3de4ff7f67755043
3736	3819	7758435872781576	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	dd07ff9225bd0dee
3839	4030	7758435873798179	esp-idf/bootloader_support/libbootloader_support.a	c689d8cbdf8b1dda
2945	4084	7758435864871068	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	5e498b4236ac3160
4030	4145	7758435875718280	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	e2b02ec7afbd18ba
4145	4277	7758435876871069	esp-idf/spi_flash/libspi_flash.a	d19c898fb21ddba1
4277	4406	7758435878181200	esp-idf/hal/libhal.a	1de4a6772283e9d
4406	4518	7758435879466921	esp-idf/micro-ecc/libmicro-ecc.a	10f520ca19390289
4658	4765	7758435881990468	esp-idf/main/libmain.a	1e7e64b3c11dcb7c
4765	4903	7758435883067125	bootloader.elf	df4d6bbecb57386d
4903	5227	7758435887644838	.bin_timestamp	e671271a776eabd9
4903	5227	7758435887644838	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	e671271a776eabd9
13	89	7758445521003392	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
10	16724	7758924782366184	build.ninja	1a1467b2f01e6ee2
10	7905	7758924782366184	build.ninja	1a1467b2f01e6ee2
10	78	7758924782706267	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
10	78	7758924782706267	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	83	7758930183215125	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	83	7758930183215125	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	91	7758930362945196	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	91	7758930362945196	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	83	7758955006765447	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
11	83	7758955006765447	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
12	82	7758955246765583	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
12	82	7758955246765583	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
