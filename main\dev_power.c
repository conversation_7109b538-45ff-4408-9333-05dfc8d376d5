#include "dev_power.h"
#include "esp_log.h"

/* LED GPIO定义 */
#define DEV_POWER_BUTTON GPIO_NUM_9
#define DEV_POWER_ENABLE GPIO_NUM_16

static const char *TAG = "DEV_POWER";
static bool led_initialized = false;
static TaskHandle_t power_task_handle = NULL;
static uint32_t check_interval = 1000; // 默认1秒

/**
 * @brief LED闪烁任务函数
 */
static void power_event_task(void *pvParameters)
{
    while (1) {
        
        // 延时
        vTaskDelay(pdMS_TO_TICKS(check_interval));
    }
}

esp_err_t dev_power_init(void)
{
    if (led_initialized) {
        ESP_LOGW(TAG, "LED already initialized");
        return ESP_OK;
    }

    // 
    gpio_config_t power_button_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,     // 禁用中断
        .mode = GPIO_MODE_OUTPUT,           // 输出模式
        .pin_bit_mask = (1ULL << DEV_POWER_BUTTON), // BUTTON_GPIO
        .pull_down_en = 0,                  
        .pull_up_en = 0,                   
    };

    //
    gpio_config_t power_enable_io_conf = {
        .intr_type = GPIO_INTR_DISABLE,     // 禁用中断
        .mode = GPIO_MODE_OUTPUT,           // 输出模式
        .pin_bit_mask = (1ULL << DEV_POWER_ENABLE), // POWER_ENABLE
        .pull_down_en = 0,                  // 
        .pull_up_en = GPIO_PULLDOWN_ENABLE,                   
    };


    esp_err_t ret = gpio_config(&power_button_io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = gpio_config(&power_enable_io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO config failed: %s", esp_err_to_name(ret));
        return ret;
    }

    // 
    ret = gpio_set_level(DEV_POWER_ENABLE, POWER_ON);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set initial power state: %s", esp_err_to_name(ret));
        return ret;
    }

    led_initialized = true;
    ESP_LOGI(TAG, "GPIO%d initialized as LED output", DEV_POWER_ENABLE);

    return ESP_OK;
}


esp_err_t dev_power_start_task(uint8_t task_priority)
{
    if (!led_initialized) {
        ESP_LOGE(TAG, "LED not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (power_task_handle != NULL) {
        ESP_LOGW(TAG, "Blink task already running");
        return ESP_OK;
    }

    BaseType_t task_ret = xTaskCreate(
        power_event_task,       // 任务函数
        "power_event",          // 任务名称
        2048,                   // 堆栈大小
        NULL,                   // 任务参数
        task_priority,          // 任务优先级
        &power_task_handle      // 任务句柄
    );

    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create Power task");
        power_task_handle = NULL;
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Power task started");
    return ESP_OK;
}
